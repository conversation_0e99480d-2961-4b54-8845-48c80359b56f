# Python bytecode files
__pycache__/
*.py[cod]
*$py.class

# Django generated files
*.log
*.pot
*.pyc
db.sqlite3
db.sqlite3-journal
media/
invoices/
invoices_data/
.DS_Store
extra_context/
memos/
invoice.json
context/
data/
saved_invoices/
test_sales_data.csv
test_local/

# Distribution / packaging
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environment
venv/
ENV/
env/

# IDE specific files
.idea/
.vscode/
*.swp
*.swo

# Local development settings
.env
.env.local 