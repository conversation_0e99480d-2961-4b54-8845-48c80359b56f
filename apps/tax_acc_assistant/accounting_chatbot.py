import os
import uuid
import time
import random
import string
import smtplib
import tempfile
import nest_asyncio
from docx import Document
from openai import OpenAI
from textblob import TextBlob
from datetime import datetime
from pydantic import BaseModel
from dotenv import load_dotenv
from collections import defaultdict
from .models import ChatConversation
from email.message import EmailMessage
from asgiref.sync import sync_to_async
from django.core.files.base import ContentFile
from django.core.files.storage import default_storage
from utils.logger import get_logger, log_exception, TAX_ACC_LOGGER

# Get a single logger for all accounting assistant functionality
logger = get_logger(TAX_ACC_LOGGER)

# Set the timezone for Toronto, Canada (Eastern Time)
TORONTO_TIMEZONE = "America/Toronto"

# Test mode flag - set to True to use static response instead of calling OpenAI API
USE_STATIC_RESPONSE_FOR_TEST = (
    os.getenv("USE_STATIC_RESPONSE_FOR_TEST", "True").lower() == "true"
)

# Static response for testing
STATIC_RESPONSE = """
Background: The question pertains to the accounting treatment of interest rate swaps for cash flow hedges under International Financial Reporting Standards (IFRS). Interest rate swaps are commonly used to manage interest rate risk by exchanging a fixed interest rate for a floating interest rate or vice versa. Cash flow hedges are a type of hedge used to hedge the exposure to variability in cash flows that is attributable to a particular risk associated with a recognized asset or liability.

Analysis: Under IFRS, the accounting treatment for interest rate swaps used as cash flow hedges involves recognizing the effective portion of the gain or loss on the swap in other comprehensive income (OCI) and the ineffective portion in profit or loss. The effective portion is determined by assessing the hedge effectiveness, which is typically done using a hedge effectiveness test.

The specific guidance for accounting for cash flow hedges, including interest rate swaps, can be found in IFRS 9 Financial Instruments. According to IFRS 9, when an interest rate swap is designated as a cash flow hedge and meets the hedge accounting criteria, any changes in the fair value of the swap that are effective in offsetting the changes in cash flows of the hedged item are recognized in OCI.

It is important to note that any ineffective portion of the hedge, i.e., the portion that does not qualify for hedge accounting treatment, is recognized immediately in profit or loss. This ensures that the financial statements accurately reflect the economic substance of the hedge.

Conclusion: In conclusion, under IFRS, interest rate swaps used as cash flow hedges are accounted for by recognizing the effective portion of the gain or loss in OCI and the ineffective portion in profit or loss. This treatment ensures that the financial statements provide a true and fair view of the hedging relationship and its impact on the entity's financial performance.

Citations:

IFRS 9 Financial Instruments
IFRS 9 provides guidance on the accounting treatment of financial instruments, including cash flow hedges.
https://www.ifrs.org/issued-standards/list-of-standards/ifrs-9-financial-instruments/
"""

# Static classification response for testing
STATIC_CLASSIFICATION = {
    "is_valid": "true",
    "category": "Accounting",
    "reasoning": "This is a question about IFRS accounting standards, specifically regarding the treatment of interest rate swaps.",
}

nest_asyncio.apply()

# Load environment variables from .env file
load_dotenv()

# Load secrets from environment variables
openai_api_key = os.getenv("OPENAI_API_KEY")

from_email = os.getenv("EMAIL_HOST_USER")
app_password = os.getenv("EMAIL_HOST_PASSWORD")
smtp_server = os.getenv("EMAIL_HOST", "smtp.hostinger.com")  # Default to hostinger
smtp_port = int(os.getenv("EMAIL_PORT", "587"))  # Default to 587 for TLS
email_enabled = (
    os.getenv("EMAIL_ENABLED", "True").lower() == "true"
)  # Enable or disable email
email_test_mode = (
    os.getenv("EMAIL_TEST_MODE", "False").lower() == "true"
)  # Just log instead of sending


llm_model = os.getenv(
    "LLM_MODEL", "gpt-4.1-mini"
)  # Default to gpt-4.1-mini if not specified

if not openai_api_key:
    raise ValueError("OPENAI_API_KEY not found in environment variables")

# Setup OpenAI client
client = OpenAI(api_key=openai_api_key)


class InquiryOutput(BaseModel):
    is_valid: bool
    category: str  # Accounting or Tax
    reasoning: str


class Agent:
    def __init__(self, name, instructions):
        self.name = name
        self.instructions = instructions


class Runner:
    @staticmethod
    async def run_classification(agent, user_input):
        # If in test mode, return the static classification
        if USE_STATIC_RESPONSE_FOR_TEST:
            logger.info("Using static classification response for test mode")
            return InquiryOutput(
                is_valid=STATIC_CLASSIFICATION["is_valid"].lower() == "true",
                category=STATIC_CLASSIFICATION["category"],
                reasoning=STATIC_CLASSIFICATION["reasoning"],
            )

        prompt = f"""
Classify this question as either Accounting or Tax, and explain why.
Also tell whether it's a valid inquiry.

Question: {user_input}

Respond in this format:
is_valid: true
category: Accounting or Tax
reasoning: [why you classified it this way]
"""
        try:
            response = client.chat.completions.create(
                model=llm_model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0,
            )
            if response.choices[0].message.content:
                text = response.choices[0].message.content.strip()
                parsed = {
                    line.split(":")[0].strip(): line.split(":")[1].strip()
                    for line in text.splitlines()
                    if ":" in line
                }
                return InquiryOutput(
                    is_valid=parsed.get("is_valid", "false").lower() == "true",
                    category=parsed.get("category", "Unknown"),
                    reasoning=parsed.get("reasoning", "No reasoning provided"),
                )
            else:
                logger.error("Empty response from OpenAI")
                return InquiryOutput(
                    is_valid=False,
                    category="Unknown",
                    reasoning="Failed to get response from AI model",
                )
        except Exception as e:
            log_exception(
                logger, e, {"function": "run_classification", "user_input": user_input}
            )
            return InquiryOutput(
                is_valid=False,
                category="Unknown",
                reasoning=f"Error processing request: {str(e)}",
            )

    @staticmethod
    async def run_memo(agent, user_input):
        # If in test mode, return the static response
        if USE_STATIC_RESPONSE_FOR_TEST:
            logger.info("Using static memo response for test mode")
            return STATIC_RESPONSE.strip()

        prompt = f"""
You are a Canadian tax/accounting expert preparing a formal technical memo for executives or government regulators.

📌 Structure the memo with the following 4 sections:
1. **Background**
   - What is the question asking?
   - What context is important?
2. **Analysis**
   - Provide a deep technical explanation, with references to authoritative sources.
   - Include law or standard names and their specific sections.
   - Cover step-by-step logic.
   - Discuss any exceptions or alternatives.
   - Where applicable, add illustrative examples (numerical or scenario-based).
   - Mention audit or reporting implications if relevant.
3. **Conclusion**
   - Summarize the key points.
   - Provide a clear answer to the original question.
4. **Citations**
   - List key sources referenced in your analysis, with a brief description of each.
   - Include URLs when possible.

📢 Important:
- Use formal, professional language.
- Be comprehensive and include all important details.
- Be specific about laws, regulations, and standards.
- When citing accounting standards, include both the standard number and the specific paragraph(s).
- Consider practical implementation implications.
- State any assumptions you're making in your analysis.

Question: {user_input}
"""
        try:
            response = client.chat.completions.create(
                model=llm_model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1,
            )
            if response.choices[0].message.content:
                return response.choices[0].message.content.strip()
            else:
                logger.error("Empty response from OpenAI")
                return "I was unable to generate a proper response. Please try again."
        except Exception as e:
            log_exception(logger, e, {"function": "run_memo", "user_input": user_input})
            return f"Error processing request: {str(e)}"

    @staticmethod
    async def run_accounting(agent, user_input):
        # If in test mode, return the static response
        if USE_STATIC_RESPONSE_FOR_TEST:
            logger.info("Using static accounting response for test mode")
            return STATIC_RESPONSE.strip()

        # Define the prompt for accounting questions
        prompt = f"""
You are a helpful accounting assistant specializing in Canadian accounting standards (IFRS, ASPE, etc.). 

Answer the following accounting question professionally, accurately, and comprehensively. 
Include references to specific accounting standards where applicable.

Question: {user_input}

Please provide:
1. A thorough explanation
2. Cite specific accounting standards by name and section
3. If relevant, briefly explain any practical implications
"""
        try:
            response = client.chat.completions.create(
                model=llm_model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1,
            )
            if response.choices[0].message.content:
                return response.choices[0].message.content.strip()
            else:
                logger.error("Empty response from OpenAI")
                return "I was unable to generate a proper response. Please try again."
        except Exception as e:
            log_exception(
                logger, e, {"function": "run_accounting", "user_input": user_input}
            )
            return f"Error processing request: {str(e)}"

    @staticmethod
    async def run_tax(agent, user_input):
        # If in test mode, return the static response
        if USE_STATIC_RESPONSE_FOR_TEST:
            logger.info("Using static tax response for test mode")
            return STATIC_RESPONSE.strip()

        # Define the prompt for tax questions
        prompt = f"""
You are a knowledgeable tax advisor specializing in Canadian taxation (federal and provincial).

Answer the following tax question comprehensively, professionally, and accurately.
Include references to specific tax laws, regulations, or CRA interpretations where applicable.

Question: {user_input}

Please provide:
1. A thorough explanation
2. Cite specific tax laws or regulations by name and section
3. If relevant, mention any filing requirements or deadlines
4. Briefly note any recent changes to relevant tax rules, if applicable
"""
        try:
            response = client.chat.completions.create(
                model=llm_model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1,
            )
            if response.choices[0].message.content:
                return response.choices[0].message.content.strip()
            else:
                logger.error("Empty response from OpenAI")
                return "I was unable to generate a proper response. Please try again."
        except Exception as e:
            log_exception(logger, e, {"function": "run_tax", "user_input": user_input})
            return f"Error processing request: {str(e)}"


# Rate limiting mechanism (in-memory)
class RateLimiter:
    def __init__(self, max_calls=10, time_window=60):
        self.max_calls = max_calls
        self.time_window = time_window  # in seconds
        self.user_calls = defaultdict(list)

    def can_process(self, user_id):
        current_time = time.time()
        # Remove expired timestamps
        self.user_calls[user_id] = [
            t for t in self.user_calls[user_id] if current_time - t < self.time_window
        ]
        # Check if under the limit
        if len(self.user_calls[user_id]) < self.max_calls:
            self.user_calls[user_id].append(current_time)
            return True
        return False


# Create a rate limiter instance
rate_limiter = RateLimiter(max_calls=10, time_window=60)  # 10 calls per minute


async def handle_accounting_inquiry(question):
    """Handle an accounting-related inquiry"""
    # Create an agent with accounting expertise
    accounting_agent = Agent(
        name="Accounting Expert",
        instructions="You are a knowledgeable accounting expert...",
    )
    # Run the accounting prompt
    response = await Runner.run_accounting(accounting_agent, question)
    return response


async def handle_tax_inquiry(question):
    """Handle a tax-related inquiry"""
    # Create an agent with tax expertise
    tax_agent = Agent(
        name="Tax Expert", instructions="You are a knowledgeable tax expert..."
    )
    # Run the tax prompt
    response = await Runner.run_tax(tax_agent, question)
    return response


def generate_memo_docx(question, memo_text, meta):
    """Generate a DOCX memo file based on the response text"""
    try:
        # Create a new Document
        doc = Document()

        # Add title
        doc.add_heading("Technical Memo", level=1)

        # Add metadata
        doc.add_paragraph(f"Date: {datetime.now().strftime('%B %d, %Y')}")
        if meta and "email" in meta:
            doc.add_paragraph(f"To: {meta['email']}")
        if meta and "company" in meta:
            doc.add_paragraph(f"Company: {meta['company']}")

        # Add question
        doc.add_heading("Subject Matter / Question", level=2)
        doc.add_paragraph(question)

        # Split memo text into sections based on common headings
        sections = memo_text.split("\n\n")
        current_section = None

        for section in sections:
            section = section.strip()
            if not section:
                continue

            # Check if this is a section heading
            is_heading = False
            for heading in ["Background:", "Analysis:", "Conclusion:", "Citations:"]:
                if section.startswith(heading) or section == heading.strip(":"):
                    is_heading = True
                    current_section = heading.strip(":")
                    doc.add_heading(current_section, level=2)
                    # Add the rest of the section text if any
                    content = section[len(heading) :].strip()
                    if content:
                        doc.add_paragraph(content)
                    break

            # If not a heading, add as paragraph to current section
            if not is_heading:
                doc.add_paragraph(section)

        # Save document to a temporary file
        with tempfile.NamedTemporaryFile(suffix=".docx", delete=False) as tmp:
            doc.save(tmp.name)
            tmp_path = tmp.name

        return tmp_path
    except Exception as e:
        log_exception(
            logger, e, {"function": "generate_memo_docx", "question": question}
        )
        return None


def send_memo_email(to, subject, summary, file_content, filename):
    """
    Send an email with the memo attached

    Args:
        to: Recipient email address
        subject: Email subject
        summary: Brief summary or response
        file_content: The binary content of the file to attach
        filename: The filename to use for the attachment

    Returns:
        bool: Whether the email was sent successfully
    """
    # Check if email is enabled
    if not email_enabled:
        logger.info("Email sending is disabled by configuration")
        return False

    # Check if credentials are available
    if not from_email or not app_password:
        logger.error("Email credentials not available")
        return False

    try:
        # Create the email message
        msg = EmailMessage()
        msg["Subject"] = subject
        msg["From"] = from_email
        msg["To"] = to

        # Add the body
        msg.set_content(
            f"""
Here is the accounting/tax memo you requested.

{summary}

This is an automated message. Please do not reply.
"""
        )

        # Attach the file
        msg.add_attachment(
            file_content,
            maintype="application",
            subtype="vnd.openxmlformats-officedocument.wordprocessingml.document",
            filename=filename,
        )

        # Log SMTP configuration
        logger.info(
            f"Sending email to {to} using SMTP server {smtp_server}:{smtp_port}"
        )

        # If in test mode, just log the email instead of sending it
        if email_test_mode:
            logger.info(f"TEST MODE: Would send email to {to} with subject '{subject}'")
            logger.info(
                f"TEST MODE: Email body contains summary and attachment: {filename}"
            )
            return True

        # Send the email using SMTP
        try:
            # Use TLS directly with port 587
            with smtplib.SMTP(smtp_server, smtp_port) as smtp:
                smtp.starttls()
                smtp.login(from_email, app_password)
                smtp.send_message(msg)
                logger.info(f"Email sent successfully to {to}")
                return True
        except Exception as e:
            logger.error(f"Failed to send email: {str(e)}")
            raise

    except Exception as e:
        log_exception(
            logger,
            e,
            {
                "function": "send_memo_email",
                "to": to,
                "subject": subject,
                "filename": filename,
                "smtp_server": smtp_server,
                "smtp_port": smtp_port,
            },
        )
        return False


def fix_grammar(input_text):
    """Fix basic grammar issues using TextBlob"""
    blob = TextBlob(input_text)
    return str(blob.correct())


async def triage_inquiry(question, meta=None):
    """
    Triage the inquiry to determine if it's valid and what category it belongs to

    Args:
        question: The user's question
        meta: Optional metadata about the request

    Returns:
        tuple: (is_valid, category, reasoning)
    """
    # Create a triage agent
    triage_agent = Agent(
        name="Triage Agent",
        instructions="You are responsible for classifying inquiries.",
    )

    # Classify the inquiry
    inquiry_output = await Runner.run_classification(triage_agent, question)

    # Log the classification
    logger.info(
        f"Classification: valid={inquiry_output.is_valid}, category={inquiry_output.category}, reasoning={inquiry_output.reasoning}"
    )

    return (
        inquiry_output.is_valid,
        inquiry_output.category,
        inquiry_output.reasoning,
    )


def get_user_email_folder(user):
    """
    Get the folder name for a user's memos.
    Use sanitized email or 'anonymous' if user is not authenticated.
    """
    if user and user.is_authenticated and user.email:
        # Sanitize email to use as folder name
        # Replace special characters with underscores
        sanitized = user.email
        special_chars = {
            "@": "_at_",
            ".": "_dot_",
            "+": "_plus_",
            "-": "_dash_",
            "=": "_equals_",
            "#": "_hash_",
            "$": "_dollar_",
            "%": "_percent_",
            "&": "_and_",
            "*": "_star_",
        }
        for char, replacement in special_chars.items():
            sanitized = sanitized.replace(char, replacement)
        return sanitized
    return "anonymous"


def generate_short_id(length=6):
    """Generate a short random ID with specified length using alphanumeric characters."""
    characters = string.ascii_letters + string.digits
    return "".join(random.choice(characters) for _ in range(length))


async def process_accounting_chat(
    question,
    memo_format=False,
    memo_details=None,
    user_id=None,
    chat_session_id=None,
    user=None,
):
    """
    Process an accounting or tax question and return the response

    Args:
        question: The user's question
        memo_format: Whether to return a formal memo
        memo_details: Optional details for memo generation
        user_id: User identifier
        chat_session_id: Chat session identifier for grouping conversations
        user: Django user object for organizing files

    Returns:
        dict: Response containing answer and metadata
    """
    # Check rate limiting if user_id is provided
    if user_id and not rate_limiter.can_process(user_id):
        return {
            "error": "Rate limit exceeded. Please try again in a minute.",
            "rate_limited": True,
        }

    try:
        # Generate a new chat session ID if not provided
        if not chat_session_id:
            chat_session_id = str(uuid.uuid4())

        # Triage the inquiry
        is_valid, category, reasoning = await triage_inquiry(question)

        # If not a valid inquiry, return error
        if not is_valid:
            return {
                "answer": f"I'm sorry, but I can only assist with accounting and tax related questions. Your question seems to be about something else: {reasoning}",
                "chat_session_id": chat_session_id,
                "valid_inquiry": False,
            }

        # Process based on category
        if category.lower() == "accounting":
            response_text = await handle_accounting_inquiry(question)
        else:  # Tax
            response_text = await handle_tax_inquiry(question)

        # Create a placeholder for memo URL
        memo_url = None

        # Generate a memo if requested
        if memo_format:
            # Generate the memo document
            memo_file = generate_memo_docx(question, response_text, memo_details)

            if memo_file:
                # Get user folder
                user_folder = get_user_email_folder(user)

                # Create a filename with memo_ prefix and a short random ID (6 characters)
                short_id = generate_short_id(6)
                filename = f"memos/{user_folder}/{chat_session_id}/memo_{short_id}.docx"

                with open(memo_file, "rb") as f:
                    file_path = default_storage.save(filename, ContentFile(f.read()))

                # Store only the filename part after "memos/" so download_memo can use it
                memo_url = file_path.replace("memos/", "")

                # Send memo by email if requested
                if memo_details and "email" in memo_details:
                    # Get the stored file from Django storage
                    try:
                        # Get the file from storage for email attachment
                        stored_file = default_storage.open(file_path, "rb")
                        file_content = stored_file.read()
                        stored_file.close()

                        email_sent = send_memo_email(
                            memo_details["email"],
                            f"Technical Memo: {question[:50]}...",
                            response_text[:200] + "...",
                            file_content,
                            os.path.basename(file_path),
                        )
                        if email_sent:
                            response_text += (
                                "\n\nA copy of this memo has been sent to your email."
                            )
                    except Exception as e:
                        log_exception(
                            logger,
                            e,
                            {
                                "function": "process_accounting_chat.email_memo",
                                "file_path": file_path,
                            },
                        )

                # Clean up the temporary file
                os.unlink(memo_file)

        # Save the conversation to the database
        corrected_question = fix_grammar(question)
        try:
            conversation = ChatConversation(
                user_id=user_id or "anonymous",
                chat_session_id=chat_session_id,
                original_question=question,
                corrected_question=corrected_question,
                category=category,
                response=response_text,
                memo_url=memo_url,
            )
            await sync_to_async(conversation.save)()
        except Exception as e:
            log_exception(
                logger,
                e,
                {
                    "function": "process_accounting_chat.save_conversation",
                    "user_id": user_id,
                    "question": question,
                },
            )

        # Return the response
        return {
            "answer": response_text,
            "chat_session_id": chat_session_id,
            "category": category,
            "memo_url": memo_url,
            "valid_inquiry": True,
        }

    except Exception as e:
        log_exception(
            logger,
            e,
            {
                "function": "process_accounting_chat",
                "user_id": user_id,
                "question": question,
            },
        )
        return {
            "error": f"An error occurred while processing your request: {str(e)}",
            "chat_session_id": (
                chat_session_id if chat_session_id else str(uuid.uuid4())
            ),
        }
