import os
import json
from django.conf import settings
from django.utils import timezone
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.core.files.storage import default_storage
from django.contrib.auth.decorators import login_required

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework import status, viewsets, permissions
from rest_framework.decorators import api_view, permission_classes

from .models import Invoice
from utils.logger import get_logger
from .invoice_processor import process_invoice

# Initialize a logger for invoice automation
invoice_logger = get_logger("invoice_automation")


def get_user_email_folder(user):
    """
    Get the folder name for a user's invoices.
    Use sanitized email or 'anonymous' if user is not authenticated.
    """
    if user and user.is_authenticated and user.email:
        # Sanitize email to use as folder name
        # Replace special characters with underscores
        sanitized = user.email
        special_chars = {
            "@": "_at_",
            ".": "_dot_",
            "+": "_plus_",
            "-": "_dash_",
            "=": "_equals_",
            "#": "_hash_",
            "$": "_dollar_",
            "%": "_percent_",
            "&": "_and_",
            "*": "_star_",
        }
        for char, replacement in special_chars.items():
            sanitized = sanitized.replace(char, replacement)
        return sanitized
    return "anonymous"


def get_invoice_path(user, filename):
    """
    Get the path where the invoice should be stored based on user email.
    """
    user_folder = get_user_email_folder(user)
    path = f"invoices/{user_folder}/{filename}"

    # Ensure directory exists
    os.makedirs(
        os.path.join(settings.MEDIA_ROOT, f"invoices/{user_folder}"), exist_ok=True
    )

    return path


def save_invoice_file(file_path, uploaded_file):
    """
    Save an invoice file, removing any existing file with the same name.

    Args:
        file_path: The path where the file should be saved
        uploaded_file: The uploaded file object

    Returns:
        The actual path where the file was saved
    """
    # Check if file already exists and delete it
    if default_storage.exists(file_path):
        invoice_logger.info(f"Deleting existing file: {file_path}")
        default_storage.delete(file_path)

    # Save the new file
    saved_path = default_storage.save(file_path, uploaded_file)
    invoice_logger.info(f"Saved new file: {saved_path}")

    return saved_path


@csrf_exempt
@api_view(["POST"])
def process_invoice_view(request):
    """Process multiple invoices using document intelligence services."""
    invoice_logger.info("Invoice processing requested")

    # Check if any files were uploaded
    if "invoice" not in request.FILES and not request.FILES.getlist("invoice"):
        invoice_logger.warning("No invoice files were uploaded")
        return Response(
            {"error": "No invoice files were uploaded"},
            status=status.HTTP_400_BAD_REQUEST,
        )

    # Get all uploaded files with the key 'invoice'
    uploaded_files = request.FILES.getlist("invoice")
    invoice_logger.info(f"Number of files uploaded: {len(uploaded_files)}")

    # List to store results for each file
    results = []

    # Process each file
    for uploaded_file in uploaded_files:
        invoice_logger.info(f"Processing file: {uploaded_file.name}")

        # Check file type
        allowed_extensions = ["pdf", "jpg", "jpeg", "png"]
        file_extension = uploaded_file.name.split(".")[-1].lower()
        if file_extension not in allowed_extensions:
            invoice_logger.warning(f"Invalid file type: {file_extension}")
            results.append(
                {
                    "file_name": uploaded_file.name,
                    "success": False,
                    "error": f"Invalid file type. Allowed types: {', '.join(allowed_extensions)}",
                }
            )
            continue

        try:
            # Get the path where the invoice should be stored
            file_path = get_invoice_path(request.user, uploaded_file.name)

            # Check if an invoice with the same name already exists for this user
            existing_invoice = None
            if request.user and request.user.is_authenticated:
                existing_invoice = Invoice.objects.filter(
                    uploaded_by=request.user, file_name=uploaded_file.name
                ).first()

            # Save the uploaded file (will delete existing file with same name)
            saved_path = save_invoice_file(file_path, uploaded_file)

            # If invoice exists, update it; otherwise create new
            if existing_invoice:
                invoice = existing_invoice
                invoice.file_path = saved_path
                invoice.status = "pending"
                invoice.uploaded_at = timezone.now()
                invoice.processed_at = None
                invoice.error_message = None
                invoice.invoice_data = None
                invoice.invoice_items = None
                invoice.extra_data = None
                invoice.save()
                invoice_logger.info(
                    f"Existing invoice record updated: {invoice.file_name}"
                )
            else:
                # Create invoice record
                invoice = Invoice.objects.create(
                    file_name=uploaded_file.name,
                    file_path=saved_path,
                    uploaded_by=request.user if request.user.is_authenticated else None,
                    status="pending",
                )
                invoice_logger.info(f"New invoice record created: {invoice.file_name}")

            # Process the invoice
            invoice_info, invoice_items, extra_tables = process_invoice(uploaded_file)

            # Log the invoice processing result
            invoice_logger.info(f"Invoice processed: {uploaded_file.name}")

            if not invoice_info:
                invoice.mark_as_error("Failed to extract information from the invoice")
                invoice_logger.error(
                    f"Failed to extract information from invoice: {uploaded_file.name}"
                )
                results.append(
                    {
                        "file_name": uploaded_file.name,
                        "success": False,
                        "error": "Failed to extract information from the invoice",
                        "invoice_id": invoice.id,
                    }
                )
                continue

            # Update invoice with extracted data
            invoice.mark_as_processed(
                invoice_data=invoice_info[0] if invoice_info else None,
                invoice_items=invoice_items,
                extra_data=extra_tables,
            )

            # Add result for this file
            results.append(
                {
                    "file_name": uploaded_file.name,
                    "success": True,
                    "invoice_id": invoice.id,
                    "invoice_info": invoice_info[0] if invoice_info else {},
                    "invoice_items": invoice_items,
                    "extra_tables": extra_tables,
                }
            )

        except Exception as e:
            invoice_logger.error(
                f"Error processing invoice {uploaded_file.name}: {str(e)}",
                exc_info=True,
            )

            # If invoice was created, mark as error
            if "invoice" in locals():
                invoice.mark_as_error(str(e))

            results.append(
                {
                    "file_name": uploaded_file.name,
                    "success": False,
                    "error": str(e),
                    "invoice_id": invoice.id if "invoice" in locals() else None,
                }
            )

    # Return the combined results
    return Response(
        {
            "total_files": len(uploaded_files),
            "successful": sum(1 for r in results if r.get("success", False)),
            "results": results,
        },
        status=status.HTTP_200_OK,
    )


class InvoiceViewSet(viewsets.ReadOnlyModelViewSet):
    """
    A viewset for viewing invoices.
    """

    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return Invoice.objects.filter(uploaded_by=self.request.user).order_by(
            "-uploaded_at"
        )

    def get_serializer_class(self):
        from .serializers import InvoiceSerializer

        return InvoiceSerializer


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def mark_invoice_verified(request, invoice_id):
    """Mark an invoice as verified"""
    try:
        invoice = Invoice.objects.get(id=invoice_id, uploaded_by=request.user)
        invoice.mark_as_verified()
        return Response({"success": True, "message": "Invoice marked as verified"})
    except Invoice.DoesNotExist:
        return Response(
            {"error": "Invoice not found"}, status=status.HTTP_404_NOT_FOUND
        )
