import api from "@/services/api";
import { motion } from "framer-motion";
import { toast } from "react-hot-toast";
import { useState, useRef } from "react";
import { API_ENDPOINTS } from "@/config/api";
import { TbArrowLeft } from "react-icons/tb";

const SalesCSVUpload = ({ onNext, onBack, variants, companyTemplate }) => {
  const [file, setFile] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef(null);

  const handleDragOver = (e) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setIsDragging(false);

    const droppedFile = e.dataTransfer.files[0];
    handleFileSelection(droppedFile);
  };

  const handleFileChange = (e) => {
    const selectedFile = e.target.files[0];
    handleFileSelection(selectedFile);
  };

  const handleFileSelection = (selectedFile) => {
    setError("");

    if (!selectedFile) return;

    // Check if file is CSV
    if (!selectedFile.name.toLowerCase().endsWith('.csv')) {
      setError("Please upload a CSV file");
      return;
    }

    // Check file size (max 10MB)
    if (selectedFile.size > 10 * 1024 * 1024) {
      setError("File size must be less than 10MB");
      return;
    }

    setFile(selectedFile);
  };

  const handleSubmit = async () => {
    if (!file) {
      setError("Please select a CSV file first");
      return;
    }

    setLoading(true);

    try {
      // Upload CSV and get column mappings
      const formData = new FormData();
      formData.append('file', file);

      const response = await api.post(API_ENDPOINTS.CSV_UPLOAD, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      if (response.data.success) {
        toast.success("CSV file processed successfully!");

        // Pass the results to the next step
        onNext({
          file: file,
          detectedColumns: response.data.detected_columns,
          suggestedMappings: response.data.suggested_mappings,
          confidenceScores: response.data.confidence_scores,
          companyTemplate: companyTemplate
        });
      }
    } catch (err) {
      console.error("Error uploading CSV:", err);
      const errorMessage = err.response?.data?.error || "Failed to process CSV file. Please try again.";
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <motion.div
      className="flex flex-col gap-6"
      variants={variants}
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <motion.button
            onClick={onBack}
            className="w-12 h-12 rounded-full flex items-center justify-center bg-blue-600 text-white shadow-md hover:bg-blue-700 transition-colors"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            aria-label="Go back"
          >
            <TbArrowLeft className="h-6 w-6" />
          </motion.button>
          <div>
            <h2 className="text-xl font-medium text-gray-700">
              Upload Sales Data
            </h2>
            <p className="text-gray-600 mt-1">
              Upload your CSV file containing sales data for {companyTemplate?.company_name}
            </p>
          </div>
        </div>
      </div>

      {/* Company Template Info */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start gap-3">
          <div className="bg-blue-100 rounded-lg p-2">
            <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
            </svg>
          </div>
          <div>
            <h3 className="font-medium text-blue-900">Using Template: {companyTemplate?.template_name}</h3>
            <p className="text-sm text-blue-700 mt-1">
              Company: {companyTemplate?.company_name} • Template: {companyTemplate?.template_display_name}
            </p>
          </div>
        </div>
      </div>

      {/* File Upload Area */}
      <motion.div
        className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
          isDragging
            ? 'border-blue-500 bg-blue-50'
            : file
            ? 'border-green-500 bg-green-50'
            : 'border-gray-300 hover:border-blue-400'
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        whileHover={{ scale: 1.01 }}
        onClick={() => fileInputRef.current?.click()}
      >
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileChange}
          accept=".csv"
          className="hidden"
        />

        {file ? (
          <div className="text-center">
            <svg className="h-12 w-12 text-green-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <p className="text-lg font-medium text-gray-800">{file.name}</p>
            <p className="text-sm text-gray-600">{(file.size / 1024 / 1024).toFixed(2)} MB</p>
            <p className="text-xs text-green-600 mt-2">Ready to process</p>
          </div>
        ) : (
          <>
            <svg className="h-12 w-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
            </svg>
            <p className="text-lg font-medium text-gray-700 mb-2">
              Drag & drop your CSV file here
            </p>
            <p className="text-sm text-gray-600 mb-4">
              or click to browse files
            </p>
            <div className="text-xs text-gray-500">
              <p>Supported format: CSV files only</p>
              <p>Maximum file size: 10MB</p>
            </div>
          </>
        )}
      </motion.div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-3">
          <div className="flex items-start gap-2">
            <svg className="w-4 h-4 text-red-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <p className="text-sm text-red-800">{error}</p>
          </div>
        </div>
      )}

      {/* CSV Format Help */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <h3 className="font-medium text-gray-900 mb-2">Expected CSV Format</h3>
        <p className="text-sm text-gray-600 mb-3">
          Your CSV should contain columns for invoice data. Common columns include:
        </p>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
          <div className="bg-white text-gray-700 px-2 py-1 rounded border">Invoice ID</div>
          <div className="bg-white text-gray-700 px-2 py-1 rounded border">Client Company</div>
          <div className="bg-white text-gray-700 px-2 py-1 rounded border">Amount</div>
          <div className="bg-white text-gray-700 px-2 py-1 rounded border">Date</div>
          <div className="bg-white text-gray-700 px-2 py-1 rounded border">Email</div>
          <div className="bg-white text-gray-700 px-2 py-1 rounded border">Description</div>
          <div className="bg-white text-gray-700 px-2 py-1 rounded border">Quantity</div>
          <div className="bg-white text-gray-700 px-2 py-1 rounded border">Unit Price</div>
        </div>
        <p className="text-xs text-gray-500 mt-2">
          Don&apos;t worry if your column names are different - we&apos;ll help you map them in the next step!
        </p>
      </div>

      {/* Submit Button */}
      <div className="flex justify-end">
        <button
          onClick={handleSubmit}
          disabled={!file || loading}
          className={`px-6 py-2 font-medium rounded-lg transition-colors ${
            !file || loading
              ? 'bg-gray-300 text-gray-600 cursor-not-allowed'
              : 'bg-blue-600 text-white hover:bg-blue-700'
          }`}
        >
          {loading ? (
            <div className="flex items-center">
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Processing...
            </div>
          ) : (
            "Process CSV File"
          )}
        </button>
      </div>
    </motion.div>
  );
};

export default SalesCSVUpload;
