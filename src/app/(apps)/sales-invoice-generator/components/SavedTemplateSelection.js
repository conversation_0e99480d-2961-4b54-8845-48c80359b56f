import api from "@/services/api";
import { motion } from "framer-motion";
import { toast } from "react-hot-toast";
import { useEffect, useState } from "react";
import { API_ENDPOINTS } from "@/config/api";
import { TbArrowLeft } from "react-icons/tb";

const SavedTemplateSelection = ({ onSelect, onBack, variants }) => {
  const [templates, setTemplates] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    fetchTemplates();
  }, []);

  const fetchTemplates = async () => {
    try {
      setLoading(true);
      const response = await api.get(API_ENDPOINTS.COMPANY_TEMPLATES);

      if (response.data.results) {
        setTemplates(response.data.results);
      }
    } catch (err) {
      console.error('Error loading templates:', err);
      setError("Failed to load saved templates. Please try again.");
      toast.error("Failed to load saved templates");
    } finally {
      setLoading(false);
    }
  };

  const handleTemplateSelect = async (template) => {
    try {
      // Start workflow with the selected template
      const response = await api.post(API_ENDPOINTS.START_WORKFLOW, {
        company_template_id: template.id
      });

      if (response.data.success) {
        toast.success("Workflow started with saved template!");

        // Pass the workflow data to the parent
        onSelect({
          workflow: response.data.workflow,
          companyTemplate: template,
          fromSavedTemplate: true
        });
      }
    } catch (err) {
      console.error('Error starting workflow:', err);
      const errorMessage = err.response?.data?.error || "Failed to start workflow. Please try again.";
      toast.error(errorMessage);
    }
  };

  const handleDeleteTemplate = async (templateId, e) => {
    e.stopPropagation(); // Prevent template selection

    if (!confirm("Are you sure you want to delete this template?")) {
      return;
    }

    try {
      await api.delete(`${API_ENDPOINTS.COMPANY_TEMPLATE_DETAIL}${templateId}/`);
      toast.success("Template deleted successfully");

      // Refresh the templates list
      fetchTemplates();
    } catch (err) {
      console.error('Error deleting template:', err);
      toast.error("Failed to delete template");
    }
  };

  return (
    <motion.div
      className="flex flex-col gap-6"
      variants={variants}
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <motion.button
            onClick={onBack}
            className="w-12 h-12 rounded-full flex items-center justify-center bg-blue-600 text-white shadow-md hover:bg-blue-700 transition-colors"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            aria-label="Go back"
          >
            <TbArrowLeft className="h-6 w-6" />
          </motion.button>
          <div>
            <h2 className="text-xl font-medium text-gray-700">
              Saved Templates
            </h2>
            <p className="text-gray-600 mt-1">
              Select from your previously created invoice templates
            </p>
          </div>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
          <div className="flex items-start gap-2">
            <svg className="w-4 h-4 text-red-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <p className="text-sm text-red-800">{error}</p>
          </div>
        </div>
      )}

      {loading ? (
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : templates.length === 0 ? (
        <div className="text-center py-8 bg-gray-50 rounded-lg">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-400 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
          </svg>
          <p className="text-gray-600 mb-2">No saved templates found</p>
          <button
            className="text-blue-600 hover:text-blue-800 font-medium"
            onClick={onBack}
          >
            Go back to create one
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-4">
          {templates.map((template) => (
            <motion.div
              key={template.id}
              className="bg-white border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-shadow p-4 cursor-pointer group"
              whileHover={{ scale: 1.01 }}
              whileTap={{ scale: 0.99 }}
              onClick={() => handleTemplateSelect(template)}
            >
              <div className="flex items-center">
                <div className="bg-purple-100 rounded-lg p-3 mr-4">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                </div>
                <div className="flex-1">
                  <h3 className="text-lg font-medium text-gray-800">{template.template_name}</h3>
                  <p className="text-sm text-gray-600">{template.company_name}</p>
                  <p className="text-xs text-gray-500">
                    Template: {template.template_display_name} •
                    {template.last_used ? ` Last used: ${new Date(template.last_used).toLocaleDateString()}` : ' Never used'}
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <button
                    onClick={(e) => handleDeleteTemplate(template.id, e)}
                    className="opacity-0 group-hover:opacity-100 p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-all"
                    title="Delete template"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      )}
    </motion.div>
  );
};

export default SavedTemplateSelection;
