import { motion } from "framer-motion";
import { useEffect, useState } from "react";
import { TbArrowLeft, TbMail, TbCheck, TbX, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>b<PERSON><PERSON> } from "react-icons/tb";
import { toast } from "react-hot-toast";
import api from "@/services/api";
import { API_ENDPOINTS } from "@/config/api";

const SalesEmailSending = ({ variants, generatedInvoices, emailTemplate, onComplete, onBack }) => {
  const [sending, setSending] = useState(false);
  const [sendingStatus, setSendingStatus] = useState([]);
  const [allSent, setAllSent] = useState(false);
  const [error, setError] = useState("");
  const [previewInvoice, setPreviewInvoice] = useState(null);

  // Initialize sending status
  useEffect(() => {
    if (generatedInvoices && generatedInvoices.length > 0 && sendingStatus.length === 0) {
      setSendingStatus(generatedInvoices.map(invoice => ({
        id: invoice.invoice_number,
        client_name: invoice.client_name,
        sent: false,
        sending: false,
        error: null,
        email: invoice.client_email || "" // Assuming client_email is available
      })));
    }
  }, [generatedInvoices, sendingStatus]);

  // Send a single email
  const sendSingleEmail = async (index) => {
    const invoice = generatedInvoices[index];
    
    // Check if customer has email
    if (!invoice.client_email) {
      setSendingStatus(prevStatus => {
        const newStatus = [...prevStatus];
        newStatus[index] = { 
          ...newStatus[index], 
          error: "No email address available for this customer" 
        };
        return newStatus;
      });
      return;
    }

    // Mark as sending
    setSendingStatus(prevStatus => {
      const newStatus = [...prevStatus];
      newStatus[index] = { ...newStatus[index], sending: true, error: null };
      return newStatus;
    });

    try {
      const emailData = {
        invoice_data: invoice,
        email_template: emailTemplate,
        recipient_email: invoice.client_email
      };

      const response = await api.post(API_ENDPOINTS.SEND_SALES_INVOICE_EMAILS, emailData);

      if (response.data.success) {
        setSendingStatus(prevStatus => {
          const newStatus = [...prevStatus];
          newStatus[index] = { ...newStatus[index], sending: false, sent: true };

          // Check if all emails are sent
          const allSent = newStatus.every(status => status.sent || status.error);
          if (allSent) {
            setTimeout(() => setAllSent(true), 0);
          }

          return newStatus;
        });
        toast.success(`Email sent to ${invoice.client_name}`);
      } else {
        throw new Error(response.data.error || "Failed to send email");
      }
    } catch (err) {
      console.error(`Error sending email to ${invoice.client_name}:`, err);
      setSendingStatus(prevStatus => {
        const newStatus = [...prevStatus];
        newStatus[index] = { 
          ...newStatus[index], 
          sending: false, 
          error: err.response?.data?.error || err.message || "Failed to send email"
        };
        return newStatus;
      });
      toast.error(`Failed to send email to ${invoice.client_name}`);
    }
  };

  // Send all emails
  const sendAllEmails = async () => {
    setSending(true);
    setError("");

    // Filter out invoices without email addresses
    const validInvoices = generatedInvoices.filter(invoice => invoice.client_email);
    
    if (validInvoices.length === 0) {
      setError("No invoices have customer email addresses available");
      setSending(false);
      return;
    }

    try {
      // Send emails sequentially to avoid overwhelming the server
      for (let i = 0; i < generatedInvoices.length; i++) {
        if (generatedInvoices[i].client_email && !sendingStatus[i]?.sent) {
          await sendSingleEmail(i);
          // Add a small delay between emails
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }
    } catch (err) {
      console.error("Error in bulk email sending:", err);
      setError("Failed to send some emails. Please check individual statuses.");
    } finally {
      setSending(false);
    }
  };

  // Preview the email template with actual customer data
  const previewEmail = (invoice) => {
    const { emailSubject, emailBody, senderEmail } = emailTemplate;

    const renderedSubject = emailSubject
      .replace(/\{\{customer_name\}\}/g, invoice.client_name)
      .replace(/\{\{customer\}\}/g, invoice.client_name)
      .replace(/\{\{invoice_number\}\}/g, invoice.invoice_number)
      .replace(/\{\{total_amount\}\}/g, invoice.total_amount)
      .replace(/\{\{company\}\}/g, "Your Company")
      .replace(/\{\{signature\}\}/g, "Your Name");

    const renderedBody = emailBody
      .replace(/\{\{customer_name\}\}/g, invoice.client_name)
      .replace(/\{\{customer\}\}/g, invoice.client_name)
      .replace(/\{\{invoice_number\}\}/g, invoice.invoice_number)
      .replace(/\{\{total_amount\}\}/g, invoice.total_amount)
      .replace(/\{\{company\}\}/g, "Your Company")
      .replace(/\{\{signature\}\}/g, "Your Name");

    return {
      subject: renderedSubject,
      body: renderedBody,
      from: senderEmail
    };
  };

  const getStatusIcon = (status) => {
    if (status.sending) {
      return <TbLoader className="h-5 w-5 text-blue-600 animate-spin" />;
    } else if (status.sent) {
      return <TbCheck className="h-5 w-5 text-green-600" />;
    } else if (status.error) {
      return <TbX className="h-5 w-5 text-red-600" />;
    } else {
      return <TbMail className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusText = (status) => {
    if (status.sending) return "Sending...";
    if (status.sent) return "Sent";
    if (status.error) return "Failed";
    return "Pending";
  };

  const getStatusColor = (status) => {
    if (status.sending) return "text-blue-600";
    if (status.sent) return "text-green-600";
    if (status.error) return "text-red-600";
    return "text-gray-500";
  };

  return (
    <motion.div
      className="flex flex-col gap-6"
      variants={variants}
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <motion.button
            onClick={onBack}
            className="w-12 h-12 rounded-full flex items-center justify-center bg-blue-600 text-white shadow-md hover:bg-blue-700 transition-colors"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            aria-label="Go back"
          >
            <TbArrowLeft className="h-6 w-6" />
          </motion.button>
          <div>
            <h2 className="text-xl font-medium text-gray-700">
              Send Invoices via Email
            </h2>
            <p className="text-gray-600 mt-1">
              Review and send emails with the attached invoices to your customers
            </p>
          </div>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <p className="text-red-800 text-sm">{error}</p>
        </div>
      )}

      {/* Summary */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium text-gray-700">Email Summary</h3>
          <button
            onClick={sendAllEmails}
            disabled={sending || allSent}
            className="px-4 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
          >
            {sending ? "Sending..." : allSent ? "All Sent" : "Send All Emails"}
          </button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
          <div>
            <span className="font-medium text-gray-600">Total Invoices:</span>
            <p className="text-gray-900">{generatedInvoices?.length || 0}</p>
          </div>
          <div>
            <span className="font-medium text-gray-600">Sent:</span>
            <p className="text-green-600">{sendingStatus.filter(s => s.sent).length}</p>
          </div>
          <div>
            <span className="font-medium text-gray-600">Failed:</span>
            <p className="text-red-600">{sendingStatus.filter(s => s.error).length}</p>
          </div>
          <div>
            <span className="font-medium text-gray-600">Pending:</span>
            <p className="text-gray-600">{sendingStatus.filter(s => !s.sent && !s.error && !s.sending).length}</p>
          </div>
        </div>
      </div>

      {/* Invoice List */}
      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-700">Invoice Email Status</h3>
        </div>
        
        <div className="divide-y divide-gray-200">
          {sendingStatus.map((status, index) => {
            const invoice = generatedInvoices[index];
            return (
              <div key={status.id} className="p-6 flex items-center justify-between">
                <div className="flex items-center gap-4">
                  {getStatusIcon(status)}
                  <div>
                    <p className="font-medium text-gray-900">{status.client_name}</p>
                    <p className="text-sm text-gray-600">Invoice: {status.id}</p>
                    <p className="text-xs text-gray-500">
                      {status.email || "No email address"}
                    </p>
                    {status.error && (
                      <p className="text-xs text-red-600 mt-1">{status.error}</p>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <span className={`text-sm font-medium ${getStatusColor(status)}`}>
                    {getStatusText(status)}
                  </span>
                  
                  <button
                    onClick={() => setPreviewInvoice(invoice)}
                    className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                    title="Preview Email"
                  >
                    <TbEye className="h-4 w-4" />
                  </button>
                  
                  {!status.sent && !status.sending && status.email && (
                    <button
                      onClick={() => sendSingleEmail(index)}
                      className="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors"
                    >
                      Send
                    </button>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Completion Actions */}
      {allSent && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-6 text-center">
          <TbCheck className="h-12 w-12 text-green-600 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-green-800 mb-2">All Emails Sent!</h3>
          <p className="text-green-700 mb-4">
            Successfully sent emails to all customers with valid email addresses.
          </p>
          <button
            onClick={() => onComplete && onComplete(true)}
            className="px-6 py-2 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700 transition-colors"
          >
            Complete Workflow
          </button>
        </div>
      )}

      {/* Email Preview Modal */}
      {previewInvoice && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-gray-900">Email Preview</h3>
                <button
                  onClick={() => setPreviewInvoice(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <TbX className="h-6 w-6" />
                </button>
              </div>
              
              {(() => {
                const preview = previewEmail(previewInvoice);
                return (
                  <div className="border rounded-lg overflow-hidden">
                    <div className="bg-gray-50 p-4 border-b">
                      <p className="font-medium text-gray-800">From: {preview.from}</p>
                      <p className="font-medium text-gray-800">To: {previewInvoice.client_email}</p>
                      <p className="font-medium text-gray-800 mt-2">Subject: {preview.subject}</p>
                    </div>
                    <div className="p-6 whitespace-pre-wrap text-gray-900">{preview.body}</div>
                  </div>
                );
              })()}
            </div>
          </div>
        </div>
      )}
    </motion.div>
  );
};

export default SalesEmailSending;
