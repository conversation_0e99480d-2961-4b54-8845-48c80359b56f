import { motion } from "framer-motion";

const OptionSelection = ({ onSelect, variants }) => {
  const options = [
    {
      id: "useSaved",
      title: "Use saved templates",
      description: "Select from your previously created invoice templates",
      color: "purple",
    },
    {
      id: "useMizu",
      title: "Use MizuFlow template",
      description: "Start with our professionally designed templates",
      color: "green",
    }
  ];

  return (
    <motion.div
      className="flex flex-col gap-6"
      variants={variants}
    >
      <h2 className="text-xl font-medium text-gray-700 text-center mb-2">
        Choose an option to get started
      </h2>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {options.map((option) => (
          <motion.div
            key={option.id}
            className={`bg-white border-2 rounded-xl shadow-md
                       hover:shadow-lg transition-shadow p-6 cursor-pointer`}
            style={{
              borderColor: getColorValue(option.color, 300)
            }}
            whileHover={{
              scale: 1.03,
              borderColor: getColorValue(option.color, 500)
            }}
            whileTap={{ scale: 0.98 }}
            variants={variants}
            onClick={() => onSelect(option.id)}
          >
            <div className="w-12 h-12 rounded-full mb-4 flex items-center justify-center"
                 style={{ backgroundColor: getColorValue(option.color, 100) }}>
              {getIcon(option.id)}
            </div>
            <h3 className="text-lg font-semibold mb-2"
                style={{ color: getColorValue(option.color, 700) }}>
              {option.title}
            </h3>
            <p className="text-gray-600">{option.description}</p>
          </motion.div>
        ))}
      </div>
    </motion.div>
  );
};

// Helper function to get color values that works with Tailwind's JIT compiler
const getColorValue = (color, shade) => {
  const colorMap = {
    blue: {
      100: '#dbeafe',
      300: '#93c5fd',
      500: '#3b82f6',
      600: '#2563eb',
      700: '#1d4ed8'
    },
    purple: {
      100: '#f3e8ff',
      300: '#d8b4fe',
      500: '#a855f7',
      600: '#9333ea',
      700: '#7e22ce'
    },
    green: {
      100: '#dcfce7',
      300: '#86efac',
      500: '#22c55e',
      600: '#16a34a',
      700: '#15803d'
    }
  };

  return colorMap[color]?.[shade] || '#000000';
};

// Helper function to get appropriate icon for each option
const getIcon = (optionId) => {
  switch (optionId) {
    case "createNew":
      return (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 13h6m-3-3v6m-9 1V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v8a2 2 0 01-2 2H5a2 2 0 01-2-2z" />
        </svg>
      );
    case "useSaved":
      return (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2" />
        </svg>
      );
    case "useMizu":
      return (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
        </svg>
      );
    default:
      return null;
  }
};

export default OptionSelection;