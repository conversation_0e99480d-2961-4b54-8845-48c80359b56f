<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice - {{ invoice_number }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #1e293b;
            background: #ffffff;
            font-size: 14px;
        }

        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 40px;
            background: white;
        }

        /* Header Section */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 60px;
            padding-bottom: 20px;
        }

        .company-logo {
            max-width: 200px;
            max-height: 80px;
        }

        .company-info {
            text-align: right;
            color: #64748b;
        }

        .company-name {
            font-size: 24px;
            font-weight: 600;
            color: #2563eb;
            margin-bottom: 8px;
        }

        .company-details {
            font-size: 13px;
            line-height: 1.5;
        }

        /* Invoice Title */
        .invoice-title {
            font-size: 32px;
            font-weight: 300;
            color: #1e293b;
            margin-bottom: 40px;
            letter-spacing: -0.5px;
        }

        /* Client and Invoice Info Section */
        .info-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 60px;
        }

        .client-info, .invoice-meta {
            width: 48%;
        }

        .section-title {
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            color: #64748b;
            margin-bottom: 12px;
        }

        .client-name {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 8px;
        }

        .client-details, .invoice-details {
            color: #64748b;
            font-size: 13px;
            line-height: 1.6;
        }

        .invoice-meta {
            text-align: right;
        }

        .invoice-number {
            font-size: 18px;
            font-weight: 600;
            color: #2563eb;
            margin-bottom: 8px;
        }

        /* Line Items Table */
        .line-items {
            margin-bottom: 40px;
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .items-table th {
            background: #f8fafc;
            padding: 16px 12px;
            text-align: left;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            color: #64748b;
            border-bottom: 1px solid #e2e8f0;
        }

        .items-table td {
            padding: 16px 12px;
            border-bottom: 1px solid #f1f5f9;
            color: #1e293b;
        }

        .items-table tr:last-child td {
            border-bottom: none;
        }

        .text-right {
            text-align: right;
        }

        .item-description {
            font-weight: 500;
        }

        /* Totals Section */
        .totals-section {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 60px;
        }

        .totals {
            width: 300px;
        }

        .total-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            font-size: 14px;
        }

        .total-row.subtotal {
            color: #64748b;
            border-bottom: 1px solid #f1f5f9;
            padding-bottom: 12px;
            margin-bottom: 8px;
        }

        .total-row.final {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            border-top: 2px solid #2563eb;
            padding-top: 16px;
            margin-top: 8px;
        }

        /* Footer */
        .footer {
            text-align: center;
            color: #64748b;
            font-size: 12px;
            line-height: 1.6;
            border-top: 1px solid #f1f5f9;
            padding-top: 30px;
        }

        .payment-terms {
            margin-bottom: 16px;
            font-weight: 500;
        }

        /* Print Styles */
        @media print {
            body {
                font-size: 12px;
            }

            .invoice-container {
                padding: 20px;
                box-shadow: none;
            }

            .header {
                margin-bottom: 40px;
            }

            .invoice-title {
                font-size: 28px;
                margin-bottom: 30px;
            }

            .info-section {
                margin-bottom: 40px;
            }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .invoice-container {
                padding: 20px;
            }

            .header {
                flex-direction: column;
                gap: 20px;
            }

            .company-info {
                text-align: left;
            }

            .info-section {
                flex-direction: column;
                gap: 30px;
            }

            .client-info, .invoice-meta {
                width: 100%;
            }

            .invoice-meta {
                text-align: left;
            }

            .totals {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <!-- Header -->
        <div class="header">
            <div class="company-logo-section">
                {{#has_logo}}
                <img src="{{ company_logo_url }}" alt="{{ company_name }}" class="company-logo">
                {{/has_logo}}
                <div class="company-name">{{ company_name }}</div>
            </div>
            <div class="company-info">
                <div class="company-details">
                    {{ company_address_line_1 }}<br>
                    {{ company_city }}, {{ company_state_province }} {{ company_postal_code }}<br>
                    {{ company_country }}<br>
                    {{ company_phone }}<br>
                    {{ company_email }}<br>
                    {{ company_website }}
                </div>
            </div>
        </div>

        <!-- Invoice Title -->
        <h1 class="invoice-title">Invoice</h1>

        <!-- Client and Invoice Info -->
        <div class="info-section">
            <div class="client-info">
                <div class="section-title">Bill To</div>
                <div class="client-name">{{ client_company }}</div>
                <div class="client-details">
                    {{ client_address }}<br>
                    {{ contact_email }}<br>
                    {{ phone_number }}
                </div>
            </div>
            <div class="invoice-meta">
                <div class="section-title">Invoice Details</div>
                <div class="invoice-number">#{{ invoice_number }}</div>
                <div class="invoice-details">
                    <strong>Date:</strong> {{ bill_date }}<br>
                    <strong>Due Date:</strong> {{ due_date }}<br>
                    <strong>PO Number:</strong> {{ order_reference }}<br>
                    <strong>Terms:</strong> {{ payment_terms }}
                </div>
            </div>
        </div>

        <!-- Line Items -->
        <div class="line-items">
            <table class="items-table">
                <thead>
                    <tr>
                        <th>Description</th>
                        <th class="text-right">Qty</th>
                        <th class="text-right">Rate</th>
                        <th class="text-right">Amount</th>
                    </tr>
                </thead>
                <tbody>
                    {{#line_items}}
                    <tr>
                        <td class="item-description">{{ service_description }}</td>
                        <td class="text-right">{{ quantity }}</td>
                        <td class="text-right">${{ unit_rate }}</td>
                        <td class="text-right">${{ line_amount }}</td>
                    </tr>
                    {{/line_items}}
                    {{^line_items}}
                    <tr>
                        <td class="item-description">{{ service_description }}</td>
                        <td class="text-right">{{ quantity }}</td>
                        <td class="text-right">${{ unit_rate }}</td>
                        <td class="text-right">${{ line_amount }}</td>
                    </tr>
                    {{/line_items}}
                </tbody>
            </table>
        </div>

        <!-- Totals -->
        <div class="totals-section">
            <div class="totals">
                <div class="total-row subtotal">
                    <span>Subtotal:</span>
                    <span>${{ subtotal }}</span>
                </div>
                {{#total_tax}}
                <div class="total-row">
                    <span>Tax:</span>
                    <span>${{ total_tax }}</span>
                </div>
                {{/total_tax}}
                {{#total_discount}}
                <div class="total-row">
                    <span>Discount:</span>
                    <span>-${{ total_discount }}</span>
                </div>
                {{/total_discount}}
                <div class="total-row final">
                    <span>Total:</span>
                    <span>${{ total_amount }}</span>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <div class="payment-terms">
                <strong>Payment Terms:</strong> {{ payment_terms }}
            </div>
            <div>
                <strong>Bank Details:</strong> {{ bank_name }}
                | Account: {{ account_number }}
                | Routing: {{ routing_number }}
            </div>
            <div style="margin-top: 8px;">
                <strong>Tax ID:</strong> {{ tax_id }}
            </div>
            <div style="margin-top: 8px;">
                <strong>Business Registration:</strong> {{ business_registration }}
            </div>
        </div>
    </div>
</body>
</html>
